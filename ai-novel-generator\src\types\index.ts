// 章节数据结构
export type Chapter = {
  id: string;
  title: string;
  content: string;
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 项目设置
export type ProjectSettings = {
  style: string; // 写作风格
  length: string; // 目标长度
  complexity: string; // 复杂度
}

// 项目数据结构
export type Project = {
  id: string;
  title: string;
  genre: string; // 类型
  theme: string; // 主题
  createdAt: Date;
  updatedAt: Date;
  outline: string; // 大纲
  chapters: Chapter[];
  settings: ProjectSettings;
}

// AI设置接口
export type AISettings = {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// 生成参数
export type GenerationParams = {
  type: 'outline' | 'chapter';
  prompt: string;
  temperature?: number;
  maxTokens?: number;
}
